// Mock better-sqlite3 for development
const fs = require('fs');
const path = require('path');

class MockDatabase {
    constructor(dbPath) {
        this.path = dbPath;
        this.data = new Map();
        this.statements = new Map();
        
        // Ensure directory exists
        const dir = path.dirname(dbPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        console.log(`📄 Mock Database: ${dbPath}`);
    }
    
    prepare(sql) {
        const statementId = Date.now() + Math.random();
        const statement = {
            sql: sql,
            run: (...params) => {
                console.log(`🔧 Mock SQL Run: ${sql.substring(0, 50)}...`);
                return { 
                    lastInsertRowid: Date.now(), 
                    changes: 1 
                };
            },
            get: (...params) => {
                console.log(`🔧 Mock SQL Get: ${sql.substring(0, 50)}...`);
                return null;
            },
            all: (...params) => {
                console.log(`🔧 Mock SQL All: ${sql.substring(0, 50)}...`);
                return [];
            }
        };
        
        this.statements.set(statementId, statement);
        return statement;
    }
    
    exec(sql) {
        console.log(`🔧 Mock SQL Exec: ${sql.substring(0, 50)}...`);
    }
    
    pragma(setting) {
        console.log(`🔧 Mock PRAGMA: ${setting}`);
    }
    
    close() {
        console.log('🔧 Mock database closed');
    }
}

module.exports = MockDatabase;
