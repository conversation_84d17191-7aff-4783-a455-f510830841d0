{"name": "facebot-multi", "version": "1.0.0", "description": "Facebook Automation Multi-Profile Application", "main": "src/main.js", "targets": {"main": {"source": "src/main.js", "isLibrary": true}, "renderer": {"source": "src/renderer/index.html", "distDir": "dist/renderer"}}, "scripts": {"start": "npm run dev", "dev": "electron .", "test-core": "node test-core.js", "migrate": "node migrate.js", "clean": "rimraf data/ profiles/mock-* dist/", "reset": "npm run clean && npm run test-core"}, "keywords": ["facebook", "automation", "multi-profile", "electron", "playwright"], "author": "Mujib", "license": "MIT", "dependencies": {"playwright": "^1.42.1", "better-sqlite3": "^9.2.2", "uuid": "^9.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "electron-store": "^8.1.0"}, "devDependencies": {"electron": "^28.0.0", "parcel": "^2.10.0", "concurrently": "^8.2.2", "@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@parcel/transformer-babel": "^2.14.4", "autoprefixer": "^10.4.16", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "rimraf": "^5.0.5"}, "build": {"appId": "com.mujib.facebot-multi", "productName": "FaceBot Multi", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}}}