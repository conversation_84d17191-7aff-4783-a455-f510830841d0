{"name": "facebot-multi", "version": "1.0.0", "description": "Facebook Automation Multi-Profile Application", "main": "src/main.js", "scripts": {"dev": "node dev-start.js", "start": "npm run dev", "test-core": "node test-core.js", "migrate": "node migrate.js", "setup-dev": "node setup-dev.js", "clean": "rimraf data/ profiles/mock-*", "reset": "npm run clean && npm run test-core"}, "keywords": ["facebook", "automation", "multi-profile", "electron", "playwright"], "author": "Mujib", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "cross-env": "^7.0.3", "rimraf": "^5.0.5"}, "dependencies": {"playwright": "^1.39.0", "better-sqlite3": "^9.2.2", "uuid": "^9.0.1"}, "build": {"appId": "com.mujib.facebot-multi", "productName": "FaceBot Multi", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}}}