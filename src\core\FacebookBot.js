// Try to load <PERSON><PERSON>, fallback to mock for development
let chromium;
try {
    chromium = require('playwright').chromium;
} catch (error) {
    console.log('⚠️  Playwright not available, using mock browser');
    chromium = {
        launch: async (options) => {
            console.log(`🚀 Mock Chromium launched`);
            console.log(`   Headless: ${options?.headless !== false ? 'true' : 'false'}`);
            console.log(`   UserDataDir: ${options?.userDataDir || 'default'}`);
            if (options?.proxy) {
                console.log(`   Proxy: ${options.proxy.server}`);
            }
            return {
                newContext: async (options) => {
                    console.log(`🌐 Mock New context created`);
                    return {
                        newPage: async () => {
                            console.log(`📄 Mock New page created`);
                            return {
                                url: () => 'https://m.facebook.com/',
                                goto: async (url, options) => {
                                    console.log(`🌐 Mock Navigate: ${url}`);
                                    await new Promise(resolve => setTimeout(resolve, 1000));
                                },
                                waitForSelector: async (selector, options) => {
                                    console.log(`🔍 Mock Wait for: ${selector}`);
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                },
                                fill: async (selector, text) => {
                                    console.log(`✏️  Mock Fill: ${selector} = ${text.substring(0, 20)}...`);
                                    await new Promise(resolve => setTimeout(resolve, 300));
                                },
                                click: async (selector) => {
                                    console.log(`👆 Mock Click: ${selector}`);
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                },
                                waitForLoadState: async (state) => {
                                    console.log(`⏳ Mock Wait for load state: ${state}`);
                                    await new Promise(resolve => setTimeout(resolve, 1000));
                                },
                                $: async (selector) => {
                                    console.log(`🔍 Mock Query: ${selector}`);
                                    return { click: async () => console.log(`👆 Mock Element Click: ${selector}`) };
                                },
                                evaluate: async (fn, ...args) => {
                                    console.log(`🔧 Mock Evaluate function`);
                                    return 'Mock Document Title';
                                },
                                close: async () => {
                                    console.log(`❌ Mock Page closed`);
                                },
                                setDefaultTimeout: (timeout) => {
                                    console.log(`⏱️  Mock Set timeout: ${timeout}ms`);
                                },
                                setDefaultNavigationTimeout: (timeout) => {
                                    console.log(`⏱️  Mock Set navigation timeout: ${timeout}ms`);
                                }
                            };
                        },
                        close: async () => {
                            console.log(`❌ Mock Context closed`);
                        }
                    };
                },
                close: async () => {
                    console.log(`❌ Mock Browser closed`);
                }
            };
        }
    };
}

const path = require('path');
const fs = require('fs').promises;

class FacebookBot {
    constructor(profile, proxy = null, dbManager = null) {
        this.profile = profile;
        this.proxy = proxy;
        this.dbManager = dbManager;
        this.browser = null;
        this.context = null;
        this.page = null;
        this.isLoggedIn = false;
        this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    }

    async initialize() {
        try {
            // Ensure profile directory exists
            if (this.profile.userDataDir) {
                await fs.mkdir(this.profile.userDataDir, { recursive: true });
            }

            // Browser launch options
            const isDev = process.env.NODE_ENV === 'development' || process.env.DEBUG === 'true';
            const launchOptions = {
                headless: isDev ? false : true, // Non-headless in development
                userDataDir: this.profile.userDataDir,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            };

            if (isDev) {
                console.log(`🔧 [${this.profile.name}] Development mode: browser will be visible`);
            }

            // Add proxy configuration if available
            if (this.proxy && this.proxy.server) {
                launchOptions.proxy = {
                    server: `${this.proxy.protocol}://${this.proxy.server}`,
                    username: this.proxy.username,
                    password: this.proxy.password
                };
            }

            // Launch browser
            this.browser = await chromium.launch(launchOptions);

            // Create context
            this.context = await this.browser.newContext({
                userAgent: this.userAgent,
                viewport: { width: 1366, height: 768 },
                locale: 'en-US'
            });

            // Create page
            this.page = await this.context.newPage();

            // Set timeouts
            this.page.setDefaultTimeout(30000);
            this.page.setDefaultNavigationTimeout(30000);

            console.log(`FacebookBot initialized for profile: ${this.profile.name}`);
            return true;
        } catch (error) {
            console.error('Failed to initialize FacebookBot:', error);
            await this.cleanup();
            throw error;
        }
    }

    async login(email, password) {
        try {
            console.log(`[${this.profile.name}] Attempting login with email: ${email}`);

            // Navigate to mobile Facebook for better reliability (like original facebot.py)
            await this.page.goto('https://m.facebook.com/', { waitUntil: 'networkidle' });

            // Wait for login form
            await this.page.waitForSelector('input[name="email"]', { timeout: 10000 });

            // Fill login form
            await this.page.fill('input[name="email"]', email);
            await this.page.fill('input[name="pass"]', password);

            // Add random delay to mimic human behavior
            await this.randomDelay(1000, 3000);

            // Submit form
            await this.page.click('button[name="login"], input[name="login"]');

            // Wait for navigation and check if login was successful
            await this.page.waitForLoadState('networkidle');

            // Check for login success indicators
            const currentUrl = this.page.url();
            const isLoginPage = currentUrl.includes('login') || currentUrl.includes('checkpoint');

            if (isLoginPage) {
                // Check for specific error messages
                const errorSelectors = [
                    '[data-testid="royal_login_error"]',
                    '.login_error_box',
                    '#error_box'
                ];

                for (const selector of errorSelectors) {
                    const errorElement = await this.page.$(selector);
                    if (errorElement) {
                        const errorText = await errorElement.textContent();
                        throw new Error(`Login failed: ${errorText}`);
                    }
                }

                throw new Error('Login failed: Still on login page');
            }

            this.isLoggedIn = true;
            console.log(`[${this.profile.name}] Login successful`);

            // Log successful login
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'login',
                    target: email,
                    status: 'success',
                    message: 'Login successful'
                });
            }

            return true;
        } catch (error) {
            console.error(`[${this.profile.name}] Login failed:`, error.message);

            // Log failed login
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'login',
                    target: email,
                    status: 'failed',
                    message: error.message
                });
            }

            throw error;
        }
    }

    async createComment(postId, commentText) {
        try {
            console.log(`[${this.profile.name}] Creating comment on post: ${postId}`);

            if (!this.isLoggedIn) {
                throw new Error('Not logged in');
            }

            // Navigate to mobile Facebook post (like original facebot.py)
            const postUrl = `https://m.facebook.com/${postId}`;
            await this.page.goto(postUrl, { waitUntil: 'networkidle' });

            // Wait for comment form
            await this.page.waitForSelector('textarea[name="comment_text"]', { timeout: 10000 });

            // Fill comment text
            await this.page.fill('textarea[name="comment_text"]', commentText);

            // Add random delay
            await this.randomDelay(2000, 4000);

            // Submit comment
            await this.page.click('input[name="post_comment"], button[type="submit"]');

            // Wait for comment to be posted
            await this.page.waitForLoadState('networkidle');

            console.log(`[${this.profile.name}] Comment posted successfully`);

            // Log successful comment
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'comment',
                    target: postId,
                    status: 'success',
                    message: `Comment posted: ${commentText.substring(0, 50)}...`
                });
            }

            return true;
        } catch (error) {
            console.error(`[${this.profile.name}] Comment failed:`, error.message);

            // Log failed comment
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'comment',
                    target: postId,
                    status: 'failed',
                    message: error.message
                });
            }

            throw error;
        }
    }

    async sharePost(postUrl, shareText = '') {
        try {
            console.log(`[${this.profile.name}] Sharing post: ${postUrl}`);

            if (!this.isLoggedIn) {
                throw new Error('Not logged in');
            }

            // Use Facebook sharer URL (like original facebot.py)
            const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`;
            await this.page.goto(shareUrl, { waitUntil: 'networkidle' });

            // Wait for share form
            await this.page.waitForSelector('textarea[name="xhpc_message"]', { timeout: 10000 });

            // Fill share text if provided
            if (shareText) {
                await this.page.fill('textarea[name="xhpc_message"]', shareText);
                await this.randomDelay(1000, 2000);
            }

            // Submit share (like original facebot.py)
            await this.page.click('button[name="__CONFIRM__"]');

            // Wait for share to complete
            await this.page.waitForLoadState('networkidle');

            console.log(`[${this.profile.name}] Post shared successfully`);

            // Log successful share
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'share',
                    target: postUrl,
                    status: 'success',
                    message: `Post shared: ${shareText.substring(0, 50)}...`
                });
            }

            return true;
        } catch (error) {
            console.error(`[${this.profile.name}] Share failed:`, error.message);

            // Log failed share
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'share',
                    target: postUrl,
                    status: 'failed',
                    message: error.message
                });
            }

            throw error;
        }
    }

    async performLike(postUrl) {
        try {
            console.log(`[${this.profile.name}] Liking post: ${postUrl}`);

            if (!this.isLoggedIn) {
                throw new Error('Not logged in');
            }

            await this.page.goto(postUrl, { waitUntil: 'networkidle' });

            // Multiple selectors for like button (different Facebook layouts)
            const likeSelectors = [
                '[data-testid="fb-ufi_likelink"]',
                '[aria-label="Like"]',
                '[data-testid="UFI2ReactionLink"]',
                'a[role="button"][aria-label*="Like"]'
            ];

            let likeButton = null;
            for (const selector of likeSelectors) {
                likeButton = await this.page.$(selector);
                if (likeButton) break;
            }

            if (!likeButton) {
                throw new Error('Like button not found');
            }

            await likeButton.click();
            await this.randomDelay(1000, 2000);

            console.log(`[${this.profile.name}] Post liked successfully`);

            // Log successful like
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'like',
                    target: postUrl,
                    status: 'success',
                    message: 'Post liked'
                });
            }

            return true;
        } catch (error) {
            console.error(`[${this.profile.name}] Like failed:`, error.message);

            // Log failed like
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'like',
                    target: postUrl,
                    status: 'failed',
                    message: error.message
                });
            }

            throw error;
        }
    }

    async visitDecoyLinks(decoyLinks, stayInterval = 30000) {
        try {
            console.log(`[${this.profile.name}] Visiting ${decoyLinks.length} decoy links`);

            for (const link of decoyLinks) {
                try {
                    await this.page.goto(link, { waitUntil: 'networkidle' });

                    // Simulate human-like scrolling
                    await this.simulateScrolling();

                    // Stay on page for specified interval
                    await this.randomDelay(stayInterval * 0.8, stayInterval * 1.2);

                    console.log(`[${this.profile.name}] Visited decoy link: ${link}`);

                    // Log decoy visit
                    if (this.dbManager) {
                        await this.dbManager.addLog({
                            profileId: this.profile.id,
                            action: 'decoy',
                            target: link,
                            status: 'success',
                            message: 'Decoy link visited'
                        });
                    }
                } catch (error) {
                    console.error(`[${this.profile.name}] Failed to visit decoy link ${link}:`, error.message);
                }
            }

            return true;
        } catch (error) {
            console.error(`[${this.profile.name}] Decoy links visit failed:`, error.message);
            throw error;
        }
    }

    async logout() {
        try {
            console.log(`[${this.profile.name}] Logging out`);

            if (!this.isLoggedIn) {
                return true;
            }

            // Navigate to mobile Facebook bookmarks (like original facebot.py)
            await this.page.goto('https://m.facebook.com/bookmarks', { waitUntil: 'networkidle' });

            // Find logout link
            const logoutLink = await this.page.$('a[href*="logout.php"]');
            if (logoutLink) {
                const href = await logoutLink.getAttribute('href');
                const fullLogoutUrl = href.startsWith('http') ? href : `https://m.facebook.com${href}`;
                await this.page.goto(fullLogoutUrl, { waitUntil: 'networkidle' });
            }

            this.isLoggedIn = false;
            console.log(`[${this.profile.name}] Logout successful`);

            // Log successful logout
            if (this.dbManager) {
                await this.dbManager.addLog({
                    profileId: this.profile.id,
                    action: 'logout',
                    target: '',
                    status: 'success',
                    message: 'Logout successful'
                });
            }

            return true;
        } catch (error) {
            console.error(`[${this.profile.name}] Logout failed:`, error.message);
            this.isLoggedIn = false; // Force logout state
            return false;
        }
    }

    async simulateScrolling() {
        try {
            // Simulate human-like scrolling behavior
            const scrollSteps = Math.floor(Math.random() * 3) + 2; // 2-4 scroll steps

            for (let i = 0; i < scrollSteps; i++) {
                const scrollDistance = Math.floor(Math.random() * 500) + 200; // 200-700px
                await this.page.evaluate((distance) => {
                    window.scrollBy(0, distance);
                }, scrollDistance);

                await this.randomDelay(1000, 3000);
            }

            // Scroll back to top
            await this.page.evaluate(() => {
                window.scrollTo(0, 0);
            });
        } catch (error) {
            console.error('Scrolling simulation failed:', error.message);
        }
    }

    async randomDelay(min, max) {
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    async cleanup() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }

            if (this.context) {
                await this.context.close();
                this.context = null;
            }

            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }

            this.isLoggedIn = false;
            console.log(`[${this.profile.name}] FacebookBot cleanup completed`);
        } catch (error) {
            console.error('Cleanup failed:', error.message);
        }
    }

    // Health check method
    async isHealthy() {
        try {
            if (!this.page || !this.browser) {
                return false;
            }

            // Check if page is still responsive
            await this.page.evaluate(() => document.title);
            return true;
        } catch (error) {
            return false;
        }
    }
}

module.exports = FacebookBot;
