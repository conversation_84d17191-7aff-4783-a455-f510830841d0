/**
 * Defines the schemas for data stored in electron-store.
 * This helps in ensuring data consistency and providing default values.
 */

const defaultProfileSchema = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    name: { type: 'string', default: '' },
    email: { type: 'string', default: '' },
    password: { type: 'string', default: '' },
    userDataDir: { type: 'string' },
    isValid: { type: 'boolean', default: true },
    isActive: { type: 'boolean', default: true },
    lastUsed: { type: 'integer', default: 0 },
    cooldownExpiresAt: { type: 'integer', default: 0 },
    proxyId: { type: ['string', 'null'], default: null },
    notes: { type: 'string', default: '' },
    type: { type: 'string', default: 'standard' },
    isGoogleLoggedIn: { type: 'boolean', default: false },
    googleEmail: { type: 'string', default: '' },
    createdAt: { type: 'integer', default: 0 },
    updatedAt: { type: 'integer', default: 0 },
  },
  required: ['id', 'userDataDir'],
};

const defaultProxySchema = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    name: { type: 'string', default: '' },
    server: { type: 'string' },
    username: { type: ['string', 'null'], default: null },
    password: { type: ['string', 'null'], default: null },
    protocol: { type: 'string', enum: ['http', 'https', 'socks4', 'socks5'], default: 'http' },
    isEnabled: { type: 'boolean', default: true },
    lastUsed: { type: 'integer', default: 0 },
    failureCount: { type: 'integer', default: 0 },
    notes: { type: 'string', default: '' },
    createdAt: { type: 'integer', default: 0 },
    updatedAt: { type: 'integer', default: 0 },
  },
  required: ['id', 'server'],
};

const defaultCampaignSchema = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    name: { type: 'string', default: '' },
    status: { type: 'string', enum: ['pending', 'running', 'paused', 'completed', 'failed'], default: 'pending' },
    profileIds: { type: 'array', items: { type: 'string' }, default: [] },
    actions: { type: 'array', items: { type: 'string' }, default: [] },
    postId: { type: 'string', default: '' },
    postUrl: { type: 'string', default: '' },
    batchSize: { type: 'integer', default: 5 },
    batchInterval: { type: 'integer', default: 60 },
    completedProfiles: { type: 'integer', default: 0 },
    totalProfiles: { type: 'integer', default: 0 },
    successfulActions: { type: 'integer', default: 0 },
    failedActions: { type: 'integer', default: 0 },
    createdAt: { type: 'integer', default: 0 },
    updatedAt: { type: 'integer', default: 0 },
  },
  required: ['id', 'name'],
};

const defaultLogSchema = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    profileId: { type: 'string', default: '' },
    campaignId: { type: 'string', default: '' },
    action: { type: 'string', default: '' },
    status: { type: 'string', enum: ['success', 'error', 'warning', 'info'], default: 'info' },
    message: { type: 'string', default: '' },
    details: { type: 'object', default: {} },
    timestamp: { type: 'integer', default: 0 },
  },
  required: ['id', 'timestamp'],
};

const schemas = {
  profiles: {
    type: 'array',
    items: defaultProfileSchema,
    default: [],
  },
  proxies: {
    type: 'array',
    items: defaultProxySchema,
    default: [],
  },
  campaigns: {
    type: 'array',
    items: defaultCampaignSchema,
    default: [],
  },
  logs: {
    type: 'array',
    items: defaultLogSchema,
    default: [],
  },
  settings: {
    type: 'object',
    properties: {
      // Facebook Settings
      delayComment: { type: 'integer', default: 4, minimum: 1 },
      delayShare: { type: 'integer', default: 7, minimum: 1 },
      delayLike: { type: 'integer', default: 2, minimum: 1 },
      delayDecoy: { type: 'integer', default: 30, minimum: 5 },
      
      // Profile Management
      profileCooldownMinutes: { type: 'integer', default: 15, minimum: 0 },
      maxActiveProfiles: { type: 'integer', default: 5, minimum: 1 },
      
      // Browser Settings
      headless: { type: 'boolean', default: false },
      windowWidth: { type: 'integer', default: 800, minimum: 400, maximum: 1920 },
      windowHeight: { type: 'integer', default: 600, minimum: 300, maximum: 1080 },
      userAgent: { type: 'string', default: '' },
      
      // Proxy Settings
      proxyRotationEnabled: { type: 'boolean', default: false },
      proxyRotationIntervalMinutes: { type: 'integer', default: 30, minimum: 5 },
      
      // UI Settings
      theme: { type: 'string', enum: ['system', 'light', 'dark'], default: 'system' },
      logLevel: { type: 'string', enum: ['info', 'debug', 'warn', 'error'], default: 'info' },
      
      // Automation Settings
      autoRetry: { type: 'boolean', default: true },
      maxRetries: { type: 'integer', default: 3, minimum: 1 },
      retryDelay: { type: 'integer', default: 5, minimum: 1 },
    },
    default: {
      delayComment: 4,
      delayShare: 7,
      delayLike: 2,
      delayDecoy: 30,
      profileCooldownMinutes: 15,
      maxActiveProfiles: 5,
      headless: false,
      windowWidth: 800,
      windowHeight: 600,
      userAgent: '',
      proxyRotationEnabled: false,
      proxyRotationIntervalMinutes: 30,
      theme: 'system',
      logLevel: 'info',
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 5,
    },
  },
};

module.exports = schemas;
