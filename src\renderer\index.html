<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaceBot Multi - Facebook Automation</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .sidebar-item {
            transition: all 0.3s ease;
        }

        .sidebar-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-right: 4px solid #fff;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-active { background-color: #10b981; }
        .status-inactive { background-color: #ef4444; }
        .status-cooldown { background-color: #f59e0b; }
        .status-running { background-color: #3b82f6; }

        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app" class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 text-white p-6">
            <div class="mb-8">
                <h1 class="text-2xl font-bold flex items-center">
                    <i class="fab fa-facebook-f mr-3"></i>
                    FaceBot Multi
                </h1>
                <p class="text-sm opacity-75 mt-1">Facebook Automation Suite</p>
            </div>

            <nav class="space-y-2">
                <a href="#" class="sidebar-item active flex items-center p-3 rounded-lg" data-page="dashboard">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="profiles">
                    <i class="fas fa-users mr-3"></i>
                    Profiles
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="automation">
                    <i class="fas fa-robot mr-3"></i>
                    Automation
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="proxies">
                    <i class="fas fa-network-wired mr-3"></i>
                    Proxies
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="logs">
                    <i class="fas fa-list-alt mr-3"></i>
                    Logs
                </a>
                <a href="#" class="sidebar-item flex items-center p-3 rounded-lg" data-page="settings">
                    <i class="fas fa-cog mr-3"></i>
                    Settings
                </a>
            </nav>

            <div class="mt-auto pt-8">
                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm">System Status</span>
                        <span class="status-indicator status-active"></span>
                    </div>
                    <div class="text-xs opacity-75">
                        <div>Profiles: <span id="profile-count">0</span></div>
                        <div>Active: <span id="active-campaigns">0</span></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 id="page-title" class="text-2xl font-semibold text-gray-800">Dashboard</h2>
                        <p id="page-subtitle" class="text-gray-600">Overview of your automation activities</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button id="refresh-btn" class="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-clock"></i>
                            <span id="current-time"></span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 p-6 overflow-auto">
                <div id="page-content" class="fade-in">
                    <!-- Dashboard content will be loaded here -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Stats Cards -->
                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Total Profiles</p>
                                    <p class="text-2xl font-semibold" id="total-profiles">0</p>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Active Campaigns</p>
                                    <p class="text-2xl font-semibold" id="active-campaigns-count">0</p>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Success Rate</p>
                                    <p class="text-2xl font-semibold" id="success-rate">0%</p>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6">
                            <div class="flex items-center">
                                <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                                    <i class="fas fa-network-wired"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Proxies</p>
                                    <p class="text-2xl font-semibold" id="total-proxies">0</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card p-6">
                        <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                        <div id="recent-activity" class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <span class="status-indicator status-active"></span>
                                    <span class="text-sm">System initialized successfully</span>
                                </div>
                                <span class="text-xs text-gray-500" id="init-time"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="loading-spinner"></div>
            <span id="loading-text">Loading...</span>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 space-y-2 z-40"></div>

    <!-- Application JavaScript -->
    <script src="app.js"></script>
</body>
</html>
