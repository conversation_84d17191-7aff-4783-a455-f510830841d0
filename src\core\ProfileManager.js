const path = require('path');
const fs = require('fs').promises;

// Try to load UUID, fallback to mock for development
let uuidv4;
try {
    uuidv4 = require('uuid').v4;
} catch (error) {
    console.log('⚠️  uuid not available in ProfileManager, using mock uuid');
    const { generateUUID } = require('../utils/mockModules');
    uuidv4 = generateUUID;
}

const FacebookBot = require('./FacebookBot');

class ProfileManager {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.activeProfiles = new Set();
        this.profilesDir = path.join(process.cwd(), 'profiles');
    }

    async initialize() {
        try {
            // Ensure profiles directory exists
            await fs.mkdir(this.profilesDir, { recursive: true });
            console.log('ProfileManager initialized successfully');
        } catch (error) {
            console.error('ProfileManager initialization failed:', error);
            throw error;
        }
    }

    async createProfile(profileData) {
        try {
            // Generate unique profile ID and userDataDir
            const profileId = uuidv4();
            const userDataDir = path.join(this.profilesDir, profileId);

            // Ensure userDataDir exists
            await fs.mkdir(userDataDir, { recursive: true });

            // Prepare profile data
            const profile = {
                id: profileId,
                name: profileData.name,
                email: profileData.email,
                password: profileData.password,
                userDataDir: userDataDir,
                proxyId: profileData.proxyId || null,
                isActive: profileData.isActive !== false,
                facebook: {
                    postLink: profileData.facebook?.postLink || '',
                    comments: profileData.facebook?.comments || [],
                    decoyLinks: profileData.facebook?.decoyLinks || [],
                    settings: {
                        delayComment: profileData.facebook?.settings?.delayComment || 4,
                        delayShare: profileData.facebook?.settings?.delayShare || 7,
                        delayLogout: profileData.facebook?.settings?.delayLogout || 3,
                        enableComments: profileData.facebook?.settings?.enableComments !== false,
                        enableShares: profileData.facebook?.settings?.enableShares !== false,
                        enableLikes: profileData.facebook?.settings?.enableLikes !== false,
                        enableDecoyLinks: profileData.facebook?.settings?.enableDecoyLinks !== false
                    }
                }
            };

            // Save to database
            const createdProfile = await this.dbManager.createProfile(profile);

            console.log(`Profile created: ${profile.name} (${profileId})`);
            return createdProfile;
        } catch (error) {
            console.error('Failed to create profile:', error);
            throw error;
        }
    }

    async getAllProfiles() {
        try {
            return await this.dbManager.getAllProfiles();
        } catch (error) {
            console.error('Failed to get profiles:', error);
            throw error;
        }
    }

    async getProfile(profileId) {
        try {
            return await this.dbManager.getProfile(profileId);
        } catch (error) {
            console.error('Failed to get profile:', error);
            throw error;
        }
    }

    async updateProfile(profileId, profileData) {
        try {
            return await this.dbManager.updateProfile(profileId, profileData);
        } catch (error) {
            console.error('Failed to update profile:', error);
            throw error;
        }
    }

    async deleteProfile(profileId) {
        try {
            const profile = await this.dbManager.getProfile(profileId);
            if (!profile) {
                throw new Error('Profile not found');
            }

            // Remove from active profiles if present
            this.activeProfiles.delete(profileId);

            // Delete userDataDir
            if (profile.userDataDir) {
                try {
                    await fs.rmdir(profile.userDataDir, { recursive: true });
                } catch (error) {
                    console.warn(`Failed to delete profile directory: ${error.message}`);
                }
            }

            // Delete from database
            await this.dbManager.deleteProfile(profileId);

            console.log(`Profile deleted: ${profile.name} (${profileId})`);
            return { success: true };
        } catch (error) {
            console.error('Failed to delete profile:', error);
            throw error;
        }
    }

    async getAvailableProfiles(excludeCooldown = true) {
        try {
            const allProfiles = await this.dbManager.getAllProfiles();
            const now = Date.now();

            return allProfiles.filter(profile => {
                // Check if profile is active
                if (!profile.isActive) {
                    return false;
                }

                // Check if profile is currently in use
                if (this.activeProfiles.has(profile.id)) {
                    return false;
                }

                // Check cooldown if enabled
                if (excludeCooldown && profile.cooldownExpiresAt && profile.cooldownExpiresAt > now) {
                    return false;
                }

                return true;
            }).sort((a, b) => {
                // Sort by lastUsed (oldest first)
                return (a.lastUsed || 0) - (b.lastUsed || 0);
            });
        } catch (error) {
            console.error('Failed to get available profiles:', error);
            throw error;
        }
    }

    async markProfileAsUsed(profileId, cooldownMinutes = 60) {
        try {
            const now = Date.now();
            const cooldownExpiresAt = now + (cooldownMinutes * 60 * 1000);

            await this.dbManager.updateProfile(profileId, {
                lastUsed: now,
                cooldownExpiresAt: cooldownExpiresAt
            });

            console.log(`Profile ${profileId} marked as used with ${cooldownMinutes}min cooldown`);
        } catch (error) {
            console.error('Failed to mark profile as used:', error);
            throw error;
        }
    }

    async validateProfile(profileId) {
        try {
            const profile = await this.dbManager.getProfile(profileId);
            if (!profile) {
                return { valid: false, error: 'Profile not found' };
            }

            // Check if userDataDir exists
            if (profile.userDataDir) {
                try {
                    await fs.access(profile.userDataDir);
                } catch (error) {
                    return { valid: false, error: 'Profile directory not found' };
                }
            }

            // Check required fields
            if (!profile.email || !profile.password) {
                return { valid: false, error: 'Missing email or password' };
            }

            return { valid: true };
        } catch (error) {
            console.error('Profile validation failed:', error);
            return { valid: false, error: error.message };
        }
    }

    async testProfile(profileId) {
        let bot = null;
        try {
            const profile = await this.dbManager.getProfile(profileId);
            if (!profile) {
                throw new Error('Profile not found');
            }

            // Validate profile first
            const validation = await this.validateProfile(profileId);
            if (!validation.valid) {
                throw new Error(validation.error);
            }

            // Get proxy if assigned
            let proxy = null;
            if (profile.proxyId) {
                proxy = await this.dbManager.getProxy(profile.proxyId);
            }

            // Create and initialize bot
            bot = new FacebookBot(profile, proxy, this.dbManager);
            await bot.initialize();

            // Attempt login
            await bot.login(profile.email, profile.password);

            // If we get here, login was successful
            await bot.logout();

            console.log(`Profile test successful: ${profile.name}`);
            return { success: true, message: 'Profile test successful' };
        } catch (error) {
            console.error(`Profile test failed: ${error.message}`);
            return { success: false, error: error.message };
        } finally {
            if (bot) {
                await bot.cleanup();
            }
        }
    }

    async importProfilesFromFacebot(accountsFilePath, commentsFilePath, sharesFilePath) {
        try {
            // Read accounts file (format: email:password)
            const accountsContent = await fs.readFile(accountsFilePath, 'utf8');
            const accounts = accountsContent.split('\n')
                .filter(line => line.trim() && line.includes(':'))
                .map(line => {
                    const [email, password] = line.trim().split(':');
                    return { email, password };
                });

            // Read comments file (format: comment1 :: comment2 :: comment3)
            let comments = [];
            if (commentsFilePath) {
                const commentsContent = await fs.readFile(commentsFilePath, 'utf8');
                comments = commentsContent.split('::')
                    .map(comment => comment.trim())
                    .filter(comment => comment);
            }

            // Read shares file (format: share1 :: share2 :: share3)
            let shares = [];
            if (sharesFilePath) {
                const sharesContent = await fs.readFile(sharesFilePath, 'utf8');
                shares = sharesContent.split('::')
                    .map(share => share.trim())
                    .filter(share => share);
            }

            // Create profiles
            const importedProfiles = [];
            for (let i = 0; i < accounts.length; i++) {
                const account = accounts[i];
                const profileData = {
                    name: `Imported Profile ${i + 1}`,
                    email: account.email,
                    password: account.password,
                    facebook: {
                        comments: comments,
                        shares: shares,
                        settings: {
                            delayComment: 4,
                            delayShare: 7,
                            delayLogout: 3,
                            enableComments: true,
                            enableShares: true,
                            enableLikes: false,
                            enableDecoyLinks: false
                        }
                    }
                };

                const profile = await this.createProfile(profileData);
                importedProfiles.push(profile);
            }

            console.log(`Imported ${importedProfiles.length} profiles from facebot`);
            return { success: true, imported: importedProfiles.length, profiles: importedProfiles };
        } catch (error) {
            console.error('Failed to import profiles from facebot:', error);
            throw error;
        }
    }

    async exportProfiles(profileIds = null) {
        try {
            let profiles;
            if (profileIds) {
                profiles = await Promise.all(
                    profileIds.map(id => this.dbManager.getProfile(id))
                );
            } else {
                profiles = await this.dbManager.getAllProfiles();
            }

            // Remove sensitive data and format for export
            const exportData = profiles.map(profile => ({
                name: profile.name,
                email: profile.email,
                facebook: profile.facebook,
                isActive: profile.isActive,
                createdAt: profile.createdAt
            }));

            return {
                version: '1.0',
                exportedAt: Date.now(),
                profiles: exportData
            };
        } catch (error) {
            console.error('Failed to export profiles:', error);
            throw error;
        }
    }

    async importProfiles(exportData, includePasswords = false) {
        try {
            if (!exportData.profiles || !Array.isArray(exportData.profiles)) {
                throw new Error('Invalid export data format');
            }

            const importedProfiles = [];
            for (const profileData of exportData.profiles) {
                if (!includePasswords) {
                    // Skip profiles without passwords if not including them
                    if (!profileData.password) {
                        continue;
                    }
                }

                const profile = await this.createProfile(profileData);
                importedProfiles.push(profile);
            }

            console.log(`Imported ${importedProfiles.length} profiles`);
            return { success: true, imported: importedProfiles.length, profiles: importedProfiles };
        } catch (error) {
            console.error('Failed to import profiles:', error);
            throw error;
        }
    }

    // Profile rotation for automation
    async getNextProfile(excludeIds = []) {
        try {
            const availableProfiles = await this.getAvailableProfiles();
            const filteredProfiles = availableProfiles.filter(
                profile => !excludeIds.includes(profile.id)
            );

            if (filteredProfiles.length === 0) {
                return null;
            }

            // Return the profile that was used least recently
            return filteredProfiles[0];
        } catch (error) {
            console.error('Failed to get next profile:', error);
            throw error;
        }
    }

    // Mark profile as active (in use)
    markProfileActive(profileId) {
        this.activeProfiles.add(profileId);
    }

    // Mark profile as inactive (not in use)
    markProfileInactive(profileId) {
        this.activeProfiles.delete(profileId);
    }

    // Get profile usage statistics
    async getProfileStats(profileId) {
        try {
            const logs = await this.dbManager.getLogs({ profileId });

            const stats = {
                totalActions: logs.length,
                successfulActions: logs.filter(log => log.status === 'success').length,
                failedActions: logs.filter(log => log.status === 'failed').length,
                lastActivity: logs.length > 0 ? Math.max(...logs.map(log => log.timestamp)) : null,
                actionBreakdown: {}
            };

            // Count actions by type
            logs.forEach(log => {
                if (!stats.actionBreakdown[log.action]) {
                    stats.actionBreakdown[log.action] = { total: 0, success: 0, failed: 0 };
                }
                stats.actionBreakdown[log.action].total++;
                if (log.status === 'success') {
                    stats.actionBreakdown[log.action].success++;
                } else if (log.status === 'failed') {
                    stats.actionBreakdown[log.action].failed++;
                }
            });

            return stats;
        } catch (error) {
            console.error('Failed to get profile stats:', error);
            throw error;
        }
    }
}

module.exports = ProfileManager;
