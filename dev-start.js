#!/usr/bin/env node

console.log('🚀 FaceBot Multi - Development Mode');
console.log('=====================================\n');

// Set development environment
process.env.NODE_ENV = 'development';
process.env.DEBUG = 'true';

console.log('🔧 Development Features:');
console.log('   • Electron DevTools enabled');
console.log('   • Browser visible (non-headless)');
console.log('   • Console logging enabled');
console.log('   • Hot reload (manual restart)');
console.log('');

console.log('💡 Development Tips:');
console.log('   • Use Ctrl+Shift+I for DevTools');
console.log('   • Use Ctrl+R to reload the app');
console.log('   • Check terminal for backend logs');
console.log('   • Check browser console for frontend logs');
console.log('');

console.log('📋 Available Commands:');
console.log('   • npm run test-core  - Test core functionality');
console.log('   • npm run migrate    - Import from facebot.py');
console.log('   • npm run clean      - Clean data/profiles');
console.log('   • npm run reset      - Clean and test');
console.log('');

console.log('🎯 Starting application...\n');

// Start the main application
require('./src/main.js');
