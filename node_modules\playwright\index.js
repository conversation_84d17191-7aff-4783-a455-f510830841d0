// <PERSON>ck Playwright for development
class MockPage {
    constructor() {
        this.url = () => 'https://m.facebook.com/';
        this.goto = async (url, options) => {
            console.log(`🌐 Mock Navigate: ${url}`);
            await this.delay(1000);
        };
        this.waitForSelector = async (selector, options) => {
            console.log(`🔍 Mock Wait for: ${selector}`);
            await this.delay(500);
        };
        this.fill = async (selector, text) => {
            console.log(`✏️  Mock Fill: ${selector} = ${text.substring(0, 20)}...`);
            await this.delay(300);
        };
        this.click = async (selector) => {
            console.log(`👆 Mock Click: ${selector}`);
            await this.delay(500);
        };
        this.waitForLoadState = async (state) => {
            console.log(`⏳ Mock Wait for load state: ${state}`);
            await this.delay(1000);
        };
        this.$ = async (selector) => {
            console.log(`🔍 Mock Query: ${selector}`);
            return { click: async () => console.log(`👆 Mock Element Click: ${selector}`) };
        };
        this.evaluate = async (fn, ...args) => {
            console.log(`🔧 Mock Evaluate function`);
            return 'Mock Document Title';
        };
        this.close = async () => {
            console.log(`❌ Mock Page closed`);
        };
        this.setDefaultTimeout = (timeout) => {
            console.log(`⏱️  Mock Set timeout: ${timeout}ms`);
        };
        this.setDefaultNavigationTimeout = (timeout) => {
            console.log(`⏱️  Mock Set navigation timeout: ${timeout}ms`);
        };
    }
    
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

class MockContext {
    constructor() {
        this.newPage = async () => {
            console.log(`📄 Mock New page created`);
            return new MockPage();
        };
        this.close = async () => {
            console.log(`❌ Mock Context closed`);
        };
    }
}

class MockBrowser {
    constructor() {
        this.newContext = async (options) => {
            console.log(`🌐 Mock New context created`);
            return new MockContext();
        };
        this.close = async () => {
            console.log(`❌ Mock Browser closed`);
        };
    }
}

const chromium = {
    launch: async (options) => {
        console.log(`🚀 Mock Chromium launched`);
        console.log(`   Headless: ${options?.headless !== false ? 'true' : 'false'}`);
        console.log(`   UserDataDir: ${options?.userDataDir || 'default'}`);
        if (options?.proxy) {
            console.log(`   Proxy: ${options.proxy.server}`);
        }
        return new MockBrowser();
    }
};

module.exports = { chromium };
