const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');

// Enable live reload for development
if (isDev) {
    console.log('🔧 Development mode enabled');
    console.log('   • DevTools will open automatically');
    console.log('   • Console logging enabled');
    console.log('   • Non-headless browser mode');
}

// Import core modules
const DatabaseManager = require('./database/DatabaseManager');
const ProfileManager = require('./core/ProfileManager');
const FacebookBot = require('./core/FacebookBot');
const AutomationController = require('./core/AutomationController');

class MainApp {
    constructor() {
        this.mainWindow = null;
        this.dbManager = null;
        this.profileManager = null;
        this.automationController = null;
        this.isReady = false;
    }

    async initialize() {
        try {
            // Initialize database
            this.dbManager = new DatabaseManager();
            await this.dbManager.initialize();

            // Initialize managers
            this.profileManager = new ProfileManager(this.dbManager);
            this.automationController = new AutomationController(this.dbManager, this.profileManager);

            this.isReady = true;
            console.log('Application initialized successfully');
        } catch (error) {
            console.error('Failed to initialize application:', error);
            dialog.showErrorBox('Initialization Error', `Failed to initialize application: ${error.message}`);
        }
    }

    createWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 1000,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            },
            icon: path.join(__dirname, '../assets/icon.png'),
            show: false,
            titleBarStyle: 'default'
        });

        // Load the app
        const htmlPath = path.join(__dirname, 'renderer/index.html');
        this.mainWindow.loadFile(htmlPath);

        // Open DevTools in development mode
        if (isDev) {
            this.mainWindow.webContents.openDevTools();
            console.log(`📄 Development mode: Loading ${htmlPath}`);
        }

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
        });

        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }

    setupIPC() {
        // Profile management IPC handlers
        ipcMain.handle('profiles:getAll', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.getAllProfiles();
        });

        ipcMain.handle('profiles:create', async (event, profileData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.createProfile(profileData);
        });

        ipcMain.handle('profiles:update', async (event, profileId, profileData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.updateProfile(profileId, profileData);
        });

        ipcMain.handle('profiles:delete', async (event, profileId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.deleteProfile(profileId);
        });

        ipcMain.handle('profiles:test', async (event, profileId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.profileManager.testProfile(profileId);
        });

        // Automation IPC handlers
        ipcMain.handle('automation:start', async (event, campaignConfig) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.startCampaign(campaignConfig);
        });

        ipcMain.handle('automation:stop', async (event, campaignId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.stopCampaign(campaignId);
        });

        ipcMain.handle('automation:getStatus', async (event, campaignId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.automationController.getCampaignStatus(campaignId);
        });

        // Proxy management IPC handlers
        ipcMain.handle('proxies:getAll', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getAllProxies();
        });

        ipcMain.handle('proxies:create', async (event, proxyData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.createProxy(proxyData);
        });

        ipcMain.handle('proxies:update', async (event, proxyId, proxyData) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateProxy(proxyId, proxyData);
        });

        ipcMain.handle('proxies:delete', async (event, proxyId) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.deleteProxy(proxyId);
        });

        // Logs IPC handlers
        ipcMain.handle('logs:getAll', async (event, filters) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getLogs(filters);
        });

        ipcMain.handle('logs:clear', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.clearLogs();
        });

        // Settings IPC handlers
        ipcMain.handle('settings:get', async () => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.getSettings();
        });

        ipcMain.handle('settings:update', async (event, settings) => {
            if (!this.isReady) throw new Error('Application not ready');
            return await this.dbManager.updateSettings(settings);
        });

        // File operations
        ipcMain.handle('file:selectFolder', async () => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openDirectory']
            });
            return result.filePaths[0];
        });

        ipcMain.handle('file:selectFile', async (event, filters) => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openFile'],
                filters: filters || []
            });
            return result.filePaths[0];
        });
    }
}

// Create app instance
const mainApp = new MainApp();

// App event handlers
app.whenReady().then(async () => {
    await mainApp.initialize();
    mainApp.createWindow();
    mainApp.setupIPC();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            mainApp.createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', async () => {
    // Cleanup before quitting
    if (mainApp.automationController) {
        await mainApp.automationController.stopAllCampaigns();
    }
    if (mainApp.dbManager) {
        await mainApp.dbManager.close();
    }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    dialog.showErrorBox('Unexpected Error', `An unexpected error occurred: ${error.message}`);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
