// Mock Electron for development testing
const EventEmitter = require('events');

class MockBrowserWindow extends EventEmitter {
    constructor(options) {
        super();
        this.options = options;
        this.webContents = new MockWebContents();
        console.log(`🖥️  Mock BrowserWindow created: ${options.width}x${options.height}`);
    }
    
    loadFile(filePath) {
        console.log(`📄 Mock Loading file: ${filePath}`);
        setTimeout(() => {
            this.emit('ready-to-show');
        }, 1000);
    }
    
    show() {
        console.log(`👁️  Mock Window shown`);
    }
    
    close() {
        console.log(`❌ Mock Window closed`);
        this.emit('closed');
    }
    
    once(event, callback) {
        super.once(event, callback);
    }
    
    on(event, callback) {
        super.on(event, callback);
    }
}

class MockWebContents extends EventEmitter {
    openDevTools() {
        console.log(`🔧 Mock DevTools opened`);
    }
}

class MockDialog {
    static async showOpenDialog(window, options) {
        console.log(`📁 Mock Dialog: showOpenDialog`);
        return { filePaths: ['mock/file/path'] };
    }
    
    static showErrorBox(title, content) {
        console.log(`❌ Mock Error Dialog: ${title} - ${content}`);
    }
}

class MockIpcMain extends EventEmitter {
    handle(channel, handler) {
        console.log(`🔗 Mock IPC Handle: ${channel}`);
        // Store handlers for potential testing
        this.handlers = this.handlers || {};
        this.handlers[channel] = handler;
    }
    
    // Mock invoke for testing
    async invoke(channel, ...args) {
        console.log(`📞 Mock IPC Invoke: ${channel}`);
        if (this.handlers && this.handlers[channel]) {
            try {
                return await this.handlers[channel](null, ...args);
            } catch (error) {
                console.error(`❌ Mock IPC Error: ${error.message}`);
                throw error;
            }
        }
        return null;
    }
}

class MockApp extends EventEmitter {
    whenReady() {
        console.log(`🚀 Mock App ready`);
        return Promise.resolve();
    }
    
    quit() {
        console.log(`🛑 Mock App quit`);
        this.emit('before-quit');
    }
    
    on(event, callback) {
        super.on(event, callback);
        
        // Auto-trigger some events for testing
        if (event === 'activate') {
            setTimeout(() => callback(), 100);
        }
    }
}

const mockApp = new MockApp();
const mockIpcMain = new MockIpcMain();
const mockDialog = MockDialog;

// Mock getAllWindows
MockBrowserWindow.getAllWindows = () => {
    console.log(`🖥️  Mock getAllWindows called`);
    return [];
};

module.exports = {
    app: mockApp,
    BrowserWindow: MockBrowserWindow,
    ipcMain: mockIpcMain,
    dialog: mockDialog
};
