// Try to load dependencies, fallback to mock for development
let Database;
try {
    Database = require('better-sqlite3');
} catch (error) {
    console.log('⚠️  better-sqlite3 not available, using mock database');
    Database = class MockDatabase {
        constructor(path) {
            this.path = path;
            this.data = new Map();
        }

        prepare(sql) {
            return {
                run: (...params) => ({ lastInsertRowid: Date.now(), changes: 1 }),
                get: (...params) => null,
                all: (...params) => []
            };
        }

        exec(sql) {
            console.log(`🔧 Mock SQL: ${sql.substring(0, 50)}...`);
        }

        pragma(setting) {
            console.log(`🔧 Mock PRAGMA: ${setting}`);
        }

        close() {
            console.log('🔧 Mock database closed');
        }
    };
}

let uuidv4;
try {
    uuidv4 = require('uuid').v4;
} catch (error) {
    console.log('⚠️  uuid not available, using mock uuid');
    uuidv4 = () => 'mock-uuid-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

const path = require('path');
const fs = require('fs').promises;

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = path.join(process.cwd(), 'data', 'facebot.db');
    }

    async initialize() {
        try {
            // Ensure data directory exists
            const dataDir = path.dirname(this.dbPath);
            await fs.mkdir(dataDir, { recursive: true });

            // Open database connection
            this.db = new Database(this.dbPath);

            // Enable foreign keys
            this.db.pragma('foreign_keys = ON');

            // Create tables
            await this.createTables();

            console.log('Database initialized successfully');
        } catch (error) {
            console.error('Database initialization failed:', error);
            throw error;
        }
    }

    async createTables() {
        const tables = [
            // Profiles table
            `CREATE TABLE IF NOT EXISTS profiles (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT,
                password TEXT,
                userDataDir TEXT,
                proxyId TEXT,
                isActive BOOLEAN DEFAULT 1,
                lastUsed INTEGER,
                cooldownExpiresAt INTEGER,
                createdAt INTEGER,
                facebook TEXT,
                FOREIGN KEY (proxyId) REFERENCES proxies (id)
            )`,

            // Proxies table
            `CREATE TABLE IF NOT EXISTS proxies (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                server TEXT NOT NULL,
                username TEXT,
                password TEXT,
                protocol TEXT DEFAULT 'http',
                isEnabled BOOLEAN DEFAULT 1,
                failureCount INTEGER DEFAULT 0,
                lastUsed INTEGER,
                createdAt INTEGER
            )`,

            // Automation logs table
            `CREATE TABLE IF NOT EXISTS automation_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                profileId TEXT,
                action TEXT,
                target TEXT,
                status TEXT,
                message TEXT,
                timestamp INTEGER,
                campaignId TEXT,
                FOREIGN KEY (profileId) REFERENCES profiles (id)
            )`,

            // Settings table
            `CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updatedAt INTEGER
            )`,

            // Campaigns table
            `CREATE TABLE IF NOT EXISTS campaigns (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                config TEXT,
                status TEXT DEFAULT 'stopped',
                createdAt INTEGER,
                startedAt INTEGER,
                completedAt INTEGER,
                totalProfiles INTEGER DEFAULT 0,
                completedProfiles INTEGER DEFAULT 0,
                successfulActions INTEGER DEFAULT 0,
                failedActions INTEGER DEFAULT 0
            )`
        ];

        for (const table of tables) {
            this.db.exec(table);
        }

        // Create indexes for better performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_profiles_active ON profiles (isActive)',
            'CREATE INDEX IF NOT EXISTS idx_profiles_lastused ON profiles (lastUsed)',
            'CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON automation_logs (timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_logs_profile ON automation_logs (profileId)',
            'CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns (status)'
        ];

        for (const index of indexes) {
            this.db.exec(index);
        }

        // Insert default settings if not exists
        await this.initializeDefaultSettings();
    }

    async initializeDefaultSettings() {
        const defaultSettings = {
            'automation.delay': '10',
            'automation.delayComment': '4',
            'automation.delayShare': '7',
            'automation.delayLogout': '3',
            'automation.batchSize': '5',
            'automation.cooldownMinutes': '60',
            'ui.theme': 'light',
            'ui.autoRefresh': 'true',
            'proxy.rotationEnabled': 'true',
            'proxy.maxFailures': '3'
        };

        const stmt = this.db.prepare('INSERT OR IGNORE INTO settings (key, value, updatedAt) VALUES (?, ?, ?)');
        for (const [key, value] of Object.entries(defaultSettings)) {
            stmt.run(key, value, Date.now());
        }
    }

    // Helper methods for better-sqlite3
    run(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(params);
            return { id: result.lastInsertRowid, changes: result.changes };
        } catch (error) {
            throw error;
        }
    }

    get(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.get(params);
        } catch (error) {
            throw error;
        }
    }

    all(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.all(params);
        } catch (error) {
            throw error;
        }
    }

    // Profile CRUD operations
    async createProfile(profileData) {
        const id = uuidv4();
        const now = Date.now();

        await this.run(
            `INSERT INTO profiles (id, name, email, password, userDataDir, proxyId,
             isActive, createdAt, facebook) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                id,
                profileData.name,
                profileData.email,
                profileData.password,
                profileData.userDataDir,
                profileData.proxyId || null,
                profileData.isActive !== false,
                now,
                JSON.stringify(profileData.facebook || {})
            ]
        );

        return await this.getProfile(id);
    }

    async getProfile(id) {
        const profile = await this.get('SELECT * FROM profiles WHERE id = ?', [id]);
        if (profile && profile.facebook) {
            profile.facebook = JSON.parse(profile.facebook);
        }
        return profile;
    }

    async getAllProfiles() {
        const profiles = await this.all('SELECT * FROM profiles ORDER BY createdAt DESC');
        return profiles.map(profile => {
            if (profile.facebook) {
                profile.facebook = JSON.parse(profile.facebook);
            }
            return profile;
        });
    }

    async updateProfile(id, profileData) {
        const updates = [];
        const values = [];

        Object.keys(profileData).forEach(key => {
            if (key === 'facebook') {
                updates.push(`${key} = ?`);
                values.push(JSON.stringify(profileData[key]));
            } else if (key !== 'id') {
                updates.push(`${key} = ?`);
                values.push(profileData[key]);
            }
        });

        if (updates.length > 0) {
            values.push(id);
            await this.run(
                `UPDATE profiles SET ${updates.join(', ')} WHERE id = ?`,
                values
            );
        }

        return await this.getProfile(id);
    }

    async deleteProfile(id) {
        await this.run('DELETE FROM profiles WHERE id = ?', [id]);
        return { success: true };
    }

    // Proxy CRUD operations
    async createProxy(proxyData) {
        const id = uuidv4();
        const now = Date.now();

        await this.run(
            `INSERT INTO proxies (id, name, server, username, password, protocol,
             isEnabled, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                id,
                proxyData.name,
                proxyData.server,
                proxyData.username || null,
                proxyData.password || null,
                proxyData.protocol || 'http',
                proxyData.isEnabled !== false,
                now
            ]
        );

        return await this.getProxy(id);
    }

    async getProxy(id) {
        return await this.get('SELECT * FROM proxies WHERE id = ?', [id]);
    }

    async getAllProxies() {
        return await this.all('SELECT * FROM proxies ORDER BY createdAt DESC');
    }

    async updateProxy(id, proxyData) {
        const updates = [];
        const values = [];

        Object.keys(proxyData).forEach(key => {
            if (key !== 'id') {
                updates.push(`${key} = ?`);
                values.push(proxyData[key]);
            }
        });

        if (updates.length > 0) {
            values.push(id);
            await this.run(
                `UPDATE proxies SET ${updates.join(', ')} WHERE id = ?`,
                values
            );
        }

        return await this.getProxy(id);
    }

    async deleteProxy(id) {
        await this.run('DELETE FROM proxies WHERE id = ?', [id]);
        return { success: true };
    }

    // Logging operations
    async addLog(logData) {
        await this.run(
            `INSERT INTO automation_logs (profileId, action, target, status, message,
             timestamp, campaignId) VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
                logData.profileId,
                logData.action,
                logData.target,
                logData.status,
                logData.message,
                Date.now(),
                logData.campaignId || null
            ]
        );
    }

    async getLogs(filters = {}) {
        let sql = 'SELECT * FROM automation_logs';
        const params = [];
        const conditions = [];

        if (filters.profileId) {
            conditions.push('profileId = ?');
            params.push(filters.profileId);
        }

        if (filters.campaignId) {
            conditions.push('campaignId = ?');
            params.push(filters.campaignId);
        }

        if (filters.status) {
            conditions.push('status = ?');
            params.push(filters.status);
        }

        if (filters.fromDate) {
            conditions.push('timestamp >= ?');
            params.push(filters.fromDate);
        }

        if (filters.toDate) {
            conditions.push('timestamp <= ?');
            params.push(filters.toDate);
        }

        if (conditions.length > 0) {
            sql += ' WHERE ' + conditions.join(' AND ');
        }

        sql += ' ORDER BY timestamp DESC';

        if (filters.limit) {
            sql += ' LIMIT ?';
            params.push(filters.limit);
        }

        return await this.all(sql, params);
    }

    async clearLogs() {
        await this.run('DELETE FROM automation_logs');
        return { success: true };
    }

    // Settings operations
    async getSettings() {
        const rows = await this.all('SELECT key, value FROM settings');
        const settings = {};
        rows.forEach(row => {
            settings[row.key] = row.value;
        });
        return settings;
    }

    async updateSettings(settings) {
        const now = Date.now();
        for (const [key, value] of Object.entries(settings)) {
            await this.run(
                'INSERT OR REPLACE INTO settings (key, value, updatedAt) VALUES (?, ?, ?)',
                [key, value, now]
            );
        }
        return { success: true };
    }

    // Campaign operations
    async createCampaign(campaignData) {
        const id = uuidv4();
        const now = Date.now();

        await this.run(
            `INSERT INTO campaigns (id, name, config, status, createdAt, totalProfiles)
             VALUES (?, ?, ?, ?, ?, ?)`,
            [
                id,
                campaignData.name,
                JSON.stringify(campaignData.config),
                'created',
                now,
                campaignData.totalProfiles || 0
            ]
        );

        return await this.getCampaign(id);
    }

    async getCampaign(id) {
        const campaign = await this.get('SELECT * FROM campaigns WHERE id = ?', [id]);
        if (campaign && campaign.config) {
            campaign.config = JSON.parse(campaign.config);
        }
        return campaign;
    }

    async updateCampaign(id, updates) {
        const updateFields = [];
        const values = [];

        Object.keys(updates).forEach(key => {
            if (key === 'config') {
                updateFields.push(`${key} = ?`);
                values.push(JSON.stringify(updates[key]));
            } else if (key !== 'id') {
                updateFields.push(`${key} = ?`);
                values.push(updates[key]);
            }
        });

        if (updateFields.length > 0) {
            values.push(id);
            await this.run(
                `UPDATE campaigns SET ${updateFields.join(', ')} WHERE id = ?`,
                values
            );
        }

        return await this.getCampaign(id);
    }

    async close() {
        if (this.db) {
            try {
                this.db.close();
            } catch (error) {
                console.error('Error closing database:', error);
            }
        }
    }
}

module.exports = DatabaseManager;
